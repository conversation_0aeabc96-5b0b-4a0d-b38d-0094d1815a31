# 本地环境
VITE_USER_NODE_ENV = development

# 公共基础路径
VITE_PUBLIC_PATH = /

# 路由模式
# Optional: hash | history
VITE_ROUTER_MODE = history

# 开发环境接口地址
VITE_APP_BASE_API = 'http://localhost:8080'

# 开发环境跨域代理，支持配置多个
VITE_PROXY = [["/api","https://xxxxx"]]
# VITE_PROXY = [["/api","https://www.fastmock.site/mock/xxxxxx"]]
# VITE_PROXY = [["/api-easymock","https://mock.xxxxx.com"],["/api-fastmock","https://www.xxxx"]]