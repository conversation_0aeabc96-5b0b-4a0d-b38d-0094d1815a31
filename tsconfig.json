{
  "compilerOptions": {
    "target": "ESNext", // 将代码编译为最新版本的 JS
    "useDefineForClassFields": true,
    "lib": [
      "ES2020",
      "DOM",
      "DOM.Iterable"
    ],
    "module": "ESNext",
    "skipLibCheck": true,
    /* Bundler mode */
    "moduleResolution": "node", // 使用 Node 的模块解析策略
    "allowImportingTsExtensions": true, //允许在模块导入语句中使用Typescript文件的扩展名（.ts）
    "allowJs": true, //允许使用js
    "resolveJsonModule": true, // 允许引入 JSON 文件
    "isolatedModules": true, // 要求所有文件都是 ES Module 模块。
    "noEmit": true, // 不输出文件,即编译后不会生成任何js文件
    "jsx": "react-jsx", //将JSX代码转换为普通的JavaScript代码
    "esModuleInterop": true, // 允许使用 import 引入使用 export 导出
    "baseUrl": "./",
    // 模块名到基于 baseUrl的路径映射的列表。
    "paths": {
      "@": [
        "src"
      ],
      "@/*": [
        "src/*"
      ]
    },
    /* Linting */
    "strict": false, // 开启所有严格的类型检查
    "noImplicitAny": false, // 允许隐式的 any 类型
    "forceConsistentCasingInFileNames": true, // 不允许对同一个文件使用不一致格式的引用
    "noUnusedLocals": true, //报告未使用的局部变量的错误
    "noUnusedParameters": true, //报告函数中未使用参数的错误
    "noFallthroughCasesInSwitch": true //确保switch语句中的任何非空情况都包含
  },
  "types": [
    "vite,client",
  ],
  "include": [
    "src/**/*.ts",
    "src/**/*.d.ts",
    "src/**/*.tsx",
    "src/**/*.vue",
    "vite.config.ts",
  ],
  "exclude": [
    "node_modules",
    "dist",
    "**/*.js"
  ]
}