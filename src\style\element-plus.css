:root {
    --el-color-primary: var(--primary);
    --el-fill-color-blank: hsl(var(--background));
  }

html.dark {
    /* 自定义深色背景颜色 */
    --el-color-primary: var(--primary);
    --el-fill-color-blank: hsl(var(--background));
}

/*下拉框*/
.el-select__wrapper{
    @apply p-4 py-3 rounded-md
}


.el-button+.el-button{
    margin-left: 0px;
}

.drawer-bg {
    position: relative;
    overflow: hidden;
    background-repeat: no-repeat;
    background-color: hsl(var(--dm-background));
  }

  .drawer-bg::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background-image: var(--track-cover-url);
    /* 使用自定义属性 */
    background-repeat: no-repeat;
    /* // background-size: 130%; 将背景图像放大到150% */
    background-position: center;
    filter: blur(60px);
    z-index: -1;
    mask-image: linear-gradient(
      to bottom,
      rgba(0, 0, 0, 0.5) 1%,
      rgba(0, 0, 0, 0)
    );
  }