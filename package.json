{"name": "vibe-music-client", "private": true, "version": "1.0.0", "type": "module", "description": "Vibe Music", "author": {"name": "<PERSON>", "email": "<EMAIL>"}, "scripts": {"dev": "vite", "build": "vite build", "preview": "vite preview", "build:test": "vite build --mode test", "preview:test": "vite preview --mode test", "lint": "eslint --ext .ts,.vue src", "lint:fix": "eslint --ext .ts,.vue src --fix", "format": "prettier --write src/", "type-check": "vue-tsc --noEmit"}, "dependencies": {"@element-plus/icons-vue": "^2.3.1", "@types/node": "^22.10.5", "@vueuse/core": "^12.5.0", "artplayer": "^5.2.2", "axios": "^1.7.9", "element-plus": "^2.9.3", "js-cookie": "^3.0.5", "nprogress": "^0.2.0", "pinia": "^2.3.1", "pinia-plugin-persistedstate": "^4.2.0", "tailwind-merge": "^2.6.0", "tailwindcss-animate": "^1.0.7", "unplugin-auto-import": "^0.19.0", "unplugin-vue-components": "^0.28.0", "vue": "^3.5.13", "vue-cropper": "^1.1.4", "vue-eslint-parser": "^9.4.3", "vue-i18n": "^11.0.1", "vue-router": "4"}, "devDependencies": {"@eslint/js": "^9.17.0", "@iconify/json": "^2.2.299", "@iconify/vue": "^4.3.0", "@types/markdown-it": "^14.1.2", "@types/nprogress": "^0.2.3", "@vitejs/plugin-vue": "^5.2.1", "@vue/tsconfig": "^0.7.0", "autoprefixer": "^10.4.20", "eslint": "^9.17.0", "eslint-config-prettier": "^9.1.0", "eslint-plugin-prettier": "^5.2.1", "eslint-plugin-vue": "^9.32.0", "globals": "^15.14.0", "highlight.js": "^11.11.1", "markdown-it": "^14.1.0", "postcss": "^8.4.49", "prettier": "^3.4.2", "sass-embedded": "^1.83.0", "tailwindcss": "^3.4.17", "typescript": "~5.6.2", "typescript-eslint": "^8.19.0", "unplugin-icons": "^22.0.0", "vite": "^6.0.5", "vue-tsc": "^2.2.0"}, "engines": {"node": ">=18.12.0", "pnpm": ">=7"}, "packageManager": "pnpm@9.15.2+sha512.93e57b0126f0df74ce6bff29680394c0ba54ec47246b9cf321f0121d8d9bb03f750a705f24edc3c1180853afd7c2c3b94196d0a3d53d3e069d9e2793ef11f321"}